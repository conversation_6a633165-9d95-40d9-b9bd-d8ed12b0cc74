// src/services/error-handling.ts
/**
 * Centralized error handling for API calls
 *
 * This module provides consistent error handling patterns for all API
 * operations in the application. It includes error classes, detection
 * utilities, and standardized error handling functions.
 */

import axios from 'axios';
import { getConfig } from '@/config';

function isDebugMode(): boolean {
  try {
    const { DEBUG_MODE } = getConfig();
    return !!DEBUG_MODE;
  } catch {
    return false;
  }
}

/**
 * Custom API error class with additional properties
 *
 * @example
 * throw new ApiError('Failed to fetch component data', 404, 'API_COMPONENT_NOT_FOUND', { detail: 'Component not found' });
 */
export class ApiError extends Error {
  status?: number;
  code?: string;
  responseData?: any;

  constructor(message: string, status?: number, code?: string, responseData?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.responseData = responseData;
  }
}

/**
 * Checks if an error is an unauthorized error (401)
 *
 * @param error - The error to check
 * @returns True if the error is an unauthorized error (401)
 *
 * @example
 * if (isUnauthorizedError(error)) {
 *   // Handle unauthorized error
 * }
 */
export const isUnauthorizedError = (error: unknown): boolean => {
  if (axios.isAxiosError(error)) {
    return error.response?.status === 401;
  }
  return (
    error instanceof Error &&
    'response' in error &&
    (error as any).response &&
    (error as any).response.status === 401
  );
};

/**
 * Extracts error code from various error sources
 * @param error - The error object to extract code from
 * @param responseData - Response data that might contain error codes
 * @returns The extracted error code or undefined
 */
const extractErrorCode = (error: unknown, responseData?: any): string | undefined => {
  // Try to get error code from multiple sources in order of preference:

  // 1. From response data (backend-provided error codes)
  if (responseData) {
    if (typeof responseData === 'object') {
      // Common error code field names
      const errorCode = responseData.errorCode ||
                       responseData.error_code ||
                       responseData.code ||
                       responseData.type ||
                       responseData.error?.code ||
                       responseData.error?.type;
      if (errorCode && typeof errorCode === 'string') {
        return errorCode;
      }
    }
  }

  // 2. From axios error code
  if (axios.isAxiosError(error)) {
    const axiosCode = (error as any).code;
    if (axiosCode && typeof axiosCode === 'string') {
      return axiosCode;
    }
  }

  // 3. From error object itself
  if (error && typeof error === 'object' && 'code' in error) {
    const errorCode = (error as any).code;
    if (errorCode && typeof errorCode === 'string') {
      return errorCode;
    }
  }

  return undefined;
};

/**
 * Centralized error handler for API calls
 *
 * @param error - The caught error
 * @param action - Description of the operation that failed (e.g., 'fetching component')
 * @param id - Optional ID of the entity being operated on
 * @returns Either throws an appropriate error or returns a fallback value
 *
 * @example
 * try {
 *   // API call
 * } catch (error) {
 *   return handleApiError(error, 'fetching component', componentId);
 * }
 */
export const handleApiError = <T>(error: unknown, action: string, id?: string): T | never => {
  // Special handling for 404s on job results - return empty results instead of throwing
  if (
    action === 'getting jobs results' &&
    (error as any).isAxiosError &&
    (error as any).response?.status === 404
  ) {
    return { results: [], totalRecords: 0 };
  }

  // Extract error information from various sources
  const responseStatus = axios.isAxiosError(error) ? error.response?.status : undefined;
  const responseData = axios.isAxiosError(error) ? error.response?.data : undefined;
  const errorCode = extractErrorCode(error, responseData);

  // Build error context parts for logging
  const parts: string[] = [];
  if (responseStatus) parts.push(`status ${responseStatus}`);
  if (errorCode) parts.push(`code ${errorCode}`);
  const extra = parts.length ? ` (${parts.join(', ')})` : '';
  const baseMessage = `[API] Error ${action}${id ? ` for ${id}` : ''}${extra}:`;

  if (isDebugMode()) {
    console.error(baseMessage, (error as any).message || error, responseData ? '\nResponse data:' : '', responseData || '');
  } else {
    console.error(baseMessage, (error as any).message || error);
  }

  // Always print a diagnostics block to help developers locate error code and fields
  try {
    const isAxios = axios.isAxiosError(error);
    const diag: Record<string, any> = {
      isAxiosError: isAxios,
      name: (error as any)?.name,
      message: (error as any)?.message,
      extractedErrorCode: errorCode, // Our extracted error code
      axiosCode: (error as any)?.code, // Original axios code
      status: responseStatus,
      statusText: isAxios ? (error as any)?.response?.statusText : undefined,
      method: isAxios ? (error as any)?.config?.method : undefined,
      url: isAxios ? (error as any)?.config?.url : undefined,
      baseURL: isAxios ? (error as any)?.config?.baseURL : undefined,
      hasResponseData: !!responseData,
      responseDataKeys: responseData && typeof responseData === 'object' ? Object.keys(responseData) : undefined,
      errorKeys: error && typeof error === 'object' ? Object.keys(error as any) : undefined,
    };
    // Include toJSON snapshot if available to inspect axios metadata without circular refs
    if (isAxios && typeof (error as any).toJSON === 'function') {
      try {
        diag.toJSON = (error as any).toJSON();
      } catch {}
    }
    console.error('[API] Error diagnostics:', diag);
    // Also log raw error objects so the full structure can be inspected in DevTools
    console.error('[API] Raw error object:', error);
    if (isAxios) {
      console.error('[API] Axios error.response:', (error as any).response);
      console.error('[API] Axios error.config:', (error as any).config);
      console.error('[API] Axios error.request:', (error as any).request);
      try {
        console.error('[API] Axios error.toJSON():', (error as any).toJSON?.());
      } catch {}
    }
  } catch {}

  // Handle unauthorized errors consistently
  if (isUnauthorizedError(error)) {
    throw new Error('unauthorized');
  }

  // Create a more informative error for consistent handling
  if (axios.isAxiosError(error)) {
    const status = error.response?.status;
    const suffixParts: string[] = [];
    if (status) suffixParts.push(`status ${status}`);
    if (errorCode) suffixParts.push(`code ${errorCode}`);
    const suffix = suffixParts.length ? ` (${suffixParts.join(', ')})` : '';
    throw new ApiError(
      `Failed ${action}${id ? ` for ${id}` : ''}${suffix}: ${error.message}`,
      status,
      errorCode,
      error.response?.data
    );
  } else if (error instanceof ApiError) {
    // Preserve status/code/responseData and add context to message
    const suffixParts: string[] = [];
    if (error.status) suffixParts.push(`status ${error.status}`);
    if (error.code) suffixParts.push(`code ${error.code}`);
    const suffix = suffixParts.length ? ` (${suffixParts.join(', ')})` : '';
    throw new ApiError(
      `Failed ${action}${id ? ` for ${id}` : ''}${suffix}: ${error.message}`,
      error.status,
      error.code,
      error.responseData
    );
  } else if (error instanceof Error) {
    // Map plain unauthorized marker errors to a clearer message
    const unauthorizedMarker = error.message && error.message.toLowerCase() === 'unauthorized';
    const msg = unauthorizedMarker
      ? `Failed ${action}${id ? ` for ${id}` : ''} (status 401): Unauthorized`
      : `Failed ${action}${id ? ` for ${id}` : ''}: ${error.message}`;
    throw new ApiError(msg, unauthorizedMarker ? 401 : undefined, unauthorizedMarker ? 'UNAUTHORIZED' : undefined);
  } else {
    throw new ApiError(`Unknown error ${action}${id ? ` for ${id}` : ''}: ${String(error)}`, undefined, 'UNKNOWN_ERROR');
  }
};

// Extend unauthorized detection to also catch plain 'unauthorized' errors thrown upstream
export const isUnauthorizedErrorLoose = (error: unknown): boolean => {
  if (isUnauthorizedError(error)) return true;
  return error instanceof Error && error.message && error.message.toLowerCase() === 'unauthorized';
};
