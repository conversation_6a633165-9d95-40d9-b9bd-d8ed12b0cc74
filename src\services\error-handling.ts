// src/services/error-handling.ts
/**
 * Centralized error handling for API calls
 *
 * This module provides consistent error handling patterns for all API
 * operations in the application. It includes error classes, detection
 * utilities, and standardized error handling functions.
 */

import axios from 'axios';
import { getConfig } from '@/config';

function isDebugMode(): boolean {
  try {
    const { DEBUG_MODE } = getConfig();
    return !!DEBUG_MODE;
  } catch {
    return false;
  }
}

/**
 * Custom API error class with additional properties
 *
 * @example
 * throw new ApiError('Failed to fetch component data', 404, { detail: 'Component not found' });
 */
export class ApiError extends Error {
  status?: number;
  responseData?: any;

  constructor(message: string, status?: number, responseData?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.responseData = responseData;
  }
}

/**
 * Checks if an error is an unauthorized error (401)
 *
 * @param error - The error to check
 * @returns True if the error is an unauthorized error (401)
 *
 * @example
 * if (isUnauthorizedError(error)) {
 *   // Handle unauthorized error
 * }
 */
export const isUnauthorizedError = (error: unknown): boolean => {
  if (axios.isAxiosError(error)) {
    return error.response?.status === 401;
  }
  return (
    error instanceof Error &&
    'response' in error &&
    (error as any).response &&
    (error as any).response.status === 401
  );
};

/**
 * Centralized error handler for API calls
 *
 * @param error - The caught error
 * @param action - Description of the operation that failed (e.g., 'fetching component')
 * @param id - Optional ID of the entity being operated on
 * @returns Either throws an appropriate error or returns a fallback value
 *
 * @example
 * try {
 *   // API call
 * } catch (error) {
 *   return handleApiError(error, 'fetching component', componentId);
 * }
 */
export const handleApiError = <T>(error: unknown, action: string, id?: string): T | never => {
  // Special handling for 404s on job results - return empty results instead of throwing
  if (
    action === 'getting jobs results' &&
    (error as any).isAxiosError &&
    (error as any).response?.status === 404
  ) {
    return { results: [], totalRecords: 0 };
  }

  // Log with consistent format and include response details if available
  const responseStatus = axios.isAxiosError(error) ? error.response?.status : undefined;
  const responseCode = axios.isAxiosError(error) ? (error as any).code : undefined;
  const responseData = axios.isAxiosError(error) ? error.response?.data : undefined;
  const parts: string[] = [];
  if (responseStatus) parts.push(`status ${responseStatus}`);
  if (responseCode) parts.push(`code ${responseCode}`);
  const extra = parts.length ? ` (${parts.join(', ')})` : '';
  const baseMessage = `[API] Error ${action}${id ? ` for ${id}` : ''}${extra}:`;
  if (isDebugMode()) {
    console.error(baseMessage, (error as any).message || error, responseData ? '\nResponse data:' : '', responseData || '');
  } else {
    console.error(baseMessage, (error as any).message || error);
  }

  // Always print a diagnostics block to help developers locate error code and fields
  try {
    const isAxios = axios.isAxiosError(error);
    const diag: Record<string, any> = {
      isAxiosError: isAxios,
      name: (error as any)?.name,
      message: (error as any)?.message,
      code: (error as any)?.code,
      status: responseStatus,
      statusText: isAxios ? (error as any)?.response?.statusText : undefined,
      method: isAxios ? (error as any)?.config?.method : undefined,
      url: isAxios ? (error as any)?.config?.url : undefined,
      baseURL: isAxios ? (error as any)?.config?.baseURL : undefined,
      hasResponseData: !!responseData,
      errorKeys: error && typeof error === 'object' ? Object.keys(error as any) : undefined,
    };
    // Include toJSON snapshot if available to inspect axios metadata without circular refs
    if (isAxios && typeof (error as any).toJSON === 'function') {
      try {
        diag.toJSON = (error as any).toJSON();
      } catch {}
    }
    console.error('[API] Error diagnostics:', diag);
    // Also log raw error objects so the full structure can be inspected in DevTools
    console.error('[API] Raw error object:', error);
    if (isAxios) {
      console.error('[API] Axios error.response:', (error as any).response);
      console.error('[API] Axios error.config:', (error as any).config);
      console.error('[API] Axios error.request:', (error as any).request);
      try {
        console.error('[API] Axios error.toJSON():', (error as any).toJSON?.());
      } catch {}
    }
  } catch {}

  // Handle unauthorized errors consistently
  if (isUnauthorizedError(error)) {
    throw new Error('unauthorized');
  }

  // Create a more informative error for consistent handling
  if (axios.isAxiosError(error)) {
    const status = error.response?.status;
    const code = (error as any).code as string | undefined;
    const suffixParts: string[] = [];
    if (status) suffixParts.push(`status ${status}`);
    if (code) suffixParts.push(`code ${code}`);
    const suffix = suffixParts.length ? ` (${suffixParts.join(', ')})` : '';
    throw new ApiError(
      `Failed ${action}${id ? ` for ${id}` : ''}${suffix}: ${error.message}`,
      status,
      error.response?.data
    );
  } else if (error instanceof ApiError) {
    // Preserve status/responseData and add context to message
    throw new ApiError(
      `Failed ${action}${id ? ` for ${id}` : ''}: ${error.message}`,
      error.status,
      error.responseData
    );
  } else if (error instanceof Error) {
    // Map plain unauthorized marker errors to a clearer message
    const unauthorizedMarker = error.message && error.message.toLowerCase() === 'unauthorized';
    const msg = unauthorizedMarker
      ? `Failed ${action}${id ? ` for ${id}` : ''} (status 401): Unauthorized`
      : `Failed ${action}${id ? ` for ${id}` : ''}: ${error.message}`;
    throw new ApiError(msg, unauthorizedMarker ? 401 : undefined);
  } else {
    throw new ApiError(`Unknown error ${action}${id ? ` for ${id}` : ''}: ${String(error)}`);
  }
};

// Extend unauthorized detection to also catch plain 'unauthorized' errors thrown upstream
export const isUnauthorizedErrorLoose = (error: unknown): boolean => {
  if (isUnauthorizedError(error)) return true;
  return error instanceof Error && error.message && error.message.toLowerCase() === 'unauthorized';
};
