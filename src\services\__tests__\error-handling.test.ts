import { describe, it, expect, vi, beforeEach } from 'vitest';
import axios, { AxiosError } from 'axios';
import { ApiError, handleApiError, isUnauthorizedError } from '../error-handling';

// Mock console methods to avoid noise in tests
const mockConsoleError = vi.fn();
const mockConsoleWarn = vi.fn();

beforeEach(() => {
  vi.clearAllMocks();
  console.error = mockConsoleError;
  console.warn = mockConsoleWarn;
});

describe('Enhanced Error Handling with Error Codes', () => {
  describe('ApiError class', () => {
    it('should create ApiError with status, code, and responseData', () => {
      const error = new ApiError('Test error', 404, 'NOT_FOUND', { detail: 'Resource not found' });
      
      expect(error.message).toBe('Test error');
      expect(error.status).toBe(404);
      expect(error.code).toBe('NOT_FOUND');
      expect(error.responseData).toEqual({ detail: 'Resource not found' });
      expect(error.name).toBe('ApiError');
    });

    it('should create ApiError with minimal parameters', () => {
      const error = new ApiError('Simple error');
      
      expect(error.message).toBe('Simple error');
      expect(error.status).toBeUndefined();
      expect(error.code).toBeUndefined();
      expect(error.responseData).toBeUndefined();
    });
  });

  describe('handleApiError function', () => {
    it('should handle axios error with backend error code', () => {
      const axiosError = {
        isAxiosError: true,
        message: 'Request failed',
        response: {
          status: 400,
          data: {
            message: 'Validation failed',
            errorCode: 'VALIDATION_ERROR'
          }
        }
      } as AxiosError;

      expect(() => handleApiError(axiosError, 'testing API')).toThrow(
        'Failed testing API (status 400, code VALIDATION_ERROR): Request failed'
      );
    });

    it('should handle axios error with multiple error code formats', () => {
      const testCases = [
        { field: 'errorCode', value: 'BACKEND_ERROR_CODE' },
        { field: 'error_code', value: 'SNAKE_CASE_ERROR' },
        { field: 'code', value: 'SIMPLE_CODE' },
        { field: 'type', value: 'ERROR_TYPE' }
      ];

      testCases.forEach(({ field, value }) => {
        const axiosError = {
          isAxiosError: true,
          message: 'Request failed',
          response: {
            status: 500,
            data: {
              message: 'Server error',
              [field]: value
            }
          }
        } as AxiosError;

        expect(() => handleApiError(axiosError, 'testing', 'test-id')).toThrow(
          `Failed testing for test-id (status 500, code ${value}): Request failed`
        );
      });
    });

    it('should handle axios error with nested error code', () => {
      const axiosError = {
        isAxiosError: true,
        message: 'Request failed',
        response: {
          status: 422,
          data: {
            message: 'Unprocessable entity',
            error: {
              code: 'NESTED_ERROR_CODE',
              type: 'VALIDATION'
            }
          }
        }
      } as AxiosError;

      expect(() => handleApiError(axiosError, 'processing data')).toThrow(
        'Failed processing data (status 422, code NESTED_ERROR_CODE): Request failed'
      );
    });

    it('should handle axios error without error code', () => {
      const axiosError = {
        isAxiosError: true,
        message: 'Network error',
        response: {
          status: 503,
          data: {
            message: 'Service unavailable'
          }
        }
      } as AxiosError;

      expect(() => handleApiError(axiosError, 'connecting')).toThrow(
        'Failed connecting (status 503): Network error'
      );
    });

    it('should handle axios error with axios code', () => {
      const axiosError = {
        isAxiosError: true,
        message: 'Connection timeout',
        code: 'ECONNABORTED',
        response: {
          status: undefined,
          data: null
        }
      } as AxiosError;

      expect(() => handleApiError(axiosError, 'fetching data')).toThrow(
        'Failed fetching data (code ECONNABORTED): Connection timeout'
      );
    });

    it('should handle existing ApiError and preserve error code', () => {
      const existingError = new ApiError('Original error', 400, 'ORIGINAL_CODE', { data: 'test' });

      expect(() => handleApiError(existingError, 'retrying operation', 'item-123')).toThrow(
        'Failed retrying operation for item-123 (status 400, code ORIGINAL_CODE): Original error'
      );
    });

    it('should handle unauthorized errors with error code', () => {
      const unauthorizedError = new Error('unauthorized');

      expect(() => handleApiError(unauthorizedError, 'accessing resource')).toThrow(
        'Failed accessing resource (status 401): Unauthorized'
      );
    });

    it('should handle generic errors with default error code', () => {
      const genericError = new Error('Something went wrong');

      expect(() => handleApiError(genericError, 'unknown operation')).toThrow(
        'Failed unknown operation: Something went wrong'
      );
    });

    it('should handle unknown error types with default error code', () => {
      const unknownError = 'String error';

      expect(() => handleApiError(unknownError, 'mysterious operation')).toThrow(
        'Unknown error mysterious operation (code UNKNOWN_ERROR): String error'
      );
    });

    it('should return empty results for 404 job results', () => {
      const axiosError = {
        isAxiosError: true,
        response: { status: 404 }
      } as AxiosError;

      const result = handleApiError(axiosError, 'getting jobs results', 'job-123');
      expect(result).toEqual({ results: [], totalRecords: 0 });
    });
  });

  describe('isUnauthorizedError function', () => {
    it('should detect axios 401 errors', () => {
      const axiosError = {
        isAxiosError: true,
        response: { status: 401 }
      } as AxiosError;

      expect(isUnauthorizedError(axiosError)).toBe(true);
    });

    it('should detect generic 401 errors', () => {
      const genericError = {
        response: { status: 401 }
      };

      expect(isUnauthorizedError(genericError)).toBe(true);
    });

    it('should not detect non-401 errors', () => {
      const axiosError = {
        isAxiosError: true,
        response: { status: 404 }
      } as AxiosError;

      expect(isUnauthorizedError(axiosError)).toBe(false);
    });
  });
});
