import { http, HttpResponse } from 'msw';
import { server } from './server';

export function mockOnce404(method: 'get' | 'post' | 'put' | 'patch' | 'delete', path: string) {
  server.use(
    (http as any)[method](path, () =>
      HttpResponse.json({ detail: 'Not found' }, { status: 404 })
    )
  );
}

export function mockNetworkError(method: 'get' | 'post' | 'put' | 'patch' | 'delete', path: string) {
  server.use((http as any)[method](path, () => HttpResponse.error()));
}

