import { apiClient } from '../httpClient';
import { Version } from '@/utils/types';
import { transformApiVersionsToVersions } from '@/utils/transformers';
import { VersionApiResponse } from '@/types/api.types';
import { handleApiError } from '../error-handling';

/**
 * Gets version details for a component with pagination support
 */
export const fetchVersionDetails = async (
  componentId: string,
  pageNumber = 1,
  pageSize = 20
): Promise<{
  versions: Version[];
  pageInfo: { pageNumber: number; pageSize: number; totalRecords: number };
}> => {
  try {
    if (!componentId) {
      console.warn('[API] No component ID provided for fetching version details');
      return {
        versions: [],
        pageInfo: { pageNumber, pageSize, totalRecords: 0 },
      };
    }

    const path = `Components/${componentId}/versions`;
    const response = await apiClient.get<VersionApiResponse>(path, {
      params: { pageNumber, pageSize },
    });

    if (!response.data?.versions) {
      return {
        versions: [],
        pageInfo: response.data?.pageInfo || { pageNumber, pageSize, totalRecords: 0 },
      };
    }

    return {
      versions: transformApiVersionsToVersions(response.data, componentId),
      pageInfo: response.data.pageInfo,
    };
  } catch (error: unknown) {
    // Standardize to throw via centralized handler with explicit type
    return handleApiError<{
      versions: Version[];
      pageInfo: { pageNumber: number; pageSize: number; totalRecords: number };
    }>(error, 'getting version details', componentId);
  }
};
