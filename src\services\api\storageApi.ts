import { storageClient } from '../httpClient';
import { handleApiError } from '../error-handling';

/**
 * Downloads a file for a specific component version
 * @param componentId - The ID of the component
 * @param version - The version number
 * @returns Promise with the blob data
 */
export const downloadVersionFile = async (componentId: string, version: string): Promise<Blob> => {
  if (!componentId || !version) {
    throw new Error('Component ID and version are required for downloading a file');
  }

  const path = `download/${componentId}/${version}`;

  try {
    const response = await storageClient.get(path, {
      responseType: 'blob',
      headers: {
        Accept: 'application/octet-stream',
        'Content-Type': 'application/json',
      },
    });

    // Validate response status
    if (response.status < 200 || response.status >= 300) {
      throw new Error(`Download failed with status ${response.status}: ${response.statusText}`);
    }

    // Validate response data exists and is valid
    if (!response.data) {
      throw new Error('No data received from download request');
    }

    // Check if we received an error response instead of blob data
    // Some APIs return JSON error responses even with blob responseType
    if (response.data.type === 'application/json') {
      // Try to read the error message from the blob
      const text = await response.data.text();
      let errorMessage = 'Download failed';
      try {
        const errorData = JSON.parse(text);
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch {
        // If JSON parsing fails, use the text as error message
        errorMessage = text || errorMessage;
      }
      throw new Error(errorMessage);
    }

    // Validate blob size - empty blobs might indicate an error
    if (response.data.size === 0) {
      throw new Error('Downloaded file is empty');
    }

    return new Blob([response.data], { type: 'application/octet-stream' });
  } catch (error) {
    return handleApiError<Blob>(error, 'downloading version file', `${componentId}/${version}`);
  }
};

/**
 * Initiates a download of a blob as a file
 * @param blob - The blob data to download
 * @param filename - The name to give the downloaded file
 */
export const downloadBlobAsFile = (blob: Blob, filename: string): void => {
  if (!(blob instanceof Blob)) {
    throw new Error('Invalid blob object provided for download');
  }

  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename || 'download.zip';
  link.style.display = 'none';

  document.body.appendChild(link);
  link.click();

  setTimeout(() => {
    if (document.body.contains(link)) {
      document.body.removeChild(link);
    }
    window.URL.revokeObjectURL(url);
  }, 100);
};
