import { useMutation } from '@tanstack/react-query';
import { downloadVersionFile, downloadBlobAsFile } from '@/services/api/storageApi';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import { getErrorMessage } from '@/utils/errors';

interface DownloadVersionFileParams {
  componentId: string;
  version: string;
  filename?: string;
}

/**
 * Hook for downloading version files
 * Uses React Query's useMutation for handling the download operation
 */
export const useVersionFileDownload = () => {
  const { t } = useTranslation();

  return useMutation<void, Error, DownloadVersionFileParams>({
    mutationFn: async ({ componentId, version, filename }) => {
      console.log(
        `[Download] Starting download for component: ${componentId}, version: ${version}`
      );

      try {
        const blob = await downloadVersionFile(componentId, version);
        console.log(`[Download] Successfully received blob:`, {
          size: blob.size,
          type: blob.type,
          componentId,
          version,
        });

        const defaultFilename = `version-${version}.zip`;
        downloadBlobAsFile(blob, filename || defaultFilename);

        console.log(`[Download] File download initiated for: ${filename || defaultFilename}`);
      } catch (error) {
        console.error(`[Download] Error during download process:`, error);
        throw error; // Re-throw to trigger onError
      }
    },
    onError: (error, variables) => {
      console.error('Download failed:', error);
      const errorMessage = getErrorMessage(error, true);
      toast.error(
        t('versionDetails.downloadError', {
          version: variables.version,
          componentId: variables.componentId,
          defaultValue: `Error downloading version ${variables.version} of component ${variables.componentId}: ${errorMessage}`,
        })
      );
    },
  });
};

// Type helper for components
export type UseVersionFileDownloadResult = ReturnType<typeof useVersionFileDownload>;
