import '@testing-library/jest-dom';
import { afterAll, afterEach, beforeAll, vi } from 'vitest';
import { server } from './server';

// Reduce noisy warnings in tests
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  // Provide minimal runtime config for modules that read window.APP_CONFIG
  (globalThis as any).window = globalThis.window ?? ({} as any);
  (globalThis as any).window.APP_CONFIG = {
    API_URL: 'https://AbdullahChishtiWeb:5257/api/',
    OAUTH_URL: 'https://AbdullahChishtiWeb:64023/',
    STORAGE_URL: 'https://AbdullahChishtiWeb:64038/api/Files',
    API_TIMEOUT: 1000,
    DEBUG_MODE: false,
  };

  console.error = (...args: any[]) => {
    if (typeof args[0] === 'string' && args[0].includes('ReactDOM.render')) return;
    originalError.call(console, ...args);
  };
  console.warn = (...args: any[]) => {
    if (typeof args[0] === 'string' && args[0].includes('componentWill')) return;
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

beforeAll(() => server.listen({ onUnhandledRequest: 'error' }));
afterEach(() => {
  server.resetHandlers();
  vi.restoreAllMocks();
});
afterAll(() => server.close());

