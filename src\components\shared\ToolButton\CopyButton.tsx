import { toast } from 'react-toastify';
import { t } from 'i18next';
import { formatToastErrorMessage } from '@/utils/errors';

import ToolButton from './ToolButton';

import copySVG from '@/assets/svgs/dark/copy.svg';

interface CopyButtonProps {
  handleCopy: () => string | undefined;
  size?: number;
}

export const CopyButton: React.FC<CopyButtonProps> = ({ handleCopy, size }) => {
  const copyToClipboard = async (content: string | undefined) => {
    try {
      if (content) await navigator.clipboard.writeText(content);
      toast.info(t('clipboard.copied') + content);
    } catch (error) {
      toast.error(formatToastErrorMessage(t('clipboard.unableToCopy'), error));
    }
  };

  const clickHandler = () => {
    copyToClipboard(handleCopy());
  };
  return <ToolButton size={size} onClick={clickHandler} icon={copySVG} />;
};
