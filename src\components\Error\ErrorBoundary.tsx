// Unified ErrorBoundary for the entire app.
// Usage: Wrap your app or feature modules with <ErrorBoundary> to catch and display errors.
// Optionally set variant="page" for full-page errors, or omit for component-level errors.
// Example:
// <ErrorBoundary fallback={<ErrorFallback />} variant="page"> ... </ErrorBoundary>
// All API and runtime errors should surface here for a consistent user experience.
//
// For programmatic error handling, use the exported useErrorHandler hook.
//
// Do not use custom error boundaries elsewhere; use this one for consistency.
//
// Auth errors should use the AuthError component.
//
// ---
//
// See ErrorFallback.tsx for the default fallback UI.
import React, { Component, ErrorInfo, PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import { getErrorMessage } from '@/utils/errors';

interface ErrorState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

interface ErrorContentProps {
  error?: Error;
  errorInfo?: ErrorInfo;
  onReset: () => void;
  variant?: 'page' | 'component';
}

const ErrorContent: React.FC<ErrorContentProps> = ({
  error,
  errorInfo,
  onReset,
  variant = 'component',
}) => {
  const { t } = useTranslation();
  const isPage = variant === 'page';
  const containerClass = isPage
    ? 'fixed inset-0 flex items-center justify-center bg-white'
    : 'p-4 border border-red-200 rounded bg-red-50';

  return (
    <div className={containerClass}>
      <div className="max-w-md text-center">
        <h1 className={`mb-4 font-bold text-red-600 ${isPage ? 'text-2xl' : 'text-xlarge'}`}>
          {t('error.title')}
        </h1>
        <p className="mb-4 text-gray-600">{error ? getErrorMessage(error, true) : t('error.generic')}</p>
        {errorInfo && (
          <details className="mb-4 text-left">
            <summary className="cursor-pointer text-gray-600">{t('error.details')}</summary>
            <pre className="mt-2 whitespace-pre-wrap text-small text-gray-500">
              {errorInfo.componentStack}
            </pre>
          </details>
        )}
        <button
          onClick={onReset}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          {t('error.retry')}
        </button>
      </div>
    </div>
  );
};

export class ErrorBoundary extends Component<
  PropsWithChildren<{ variant?: 'page' | 'component' }>,
  ErrorState
> {
  constructor(props: PropsWithChildren<{ variant?: 'page' | 'component' }>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Error caught by boundary:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReset = (): void => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render(): React.ReactNode {
    if (this.state.hasError) {
      return (
        <ErrorContent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          onReset={this.handleReset}
          variant={this.props.variant}
        />
      );
    }
    return this.props.children;
  }
}

// Export a hook for programmatic error handling
export const useErrorHandler = () => {
  const { t } = useTranslation();
  return {
    handleError: (error: Error) => {
      console.error('Handled error:', error);
      return (
        <ErrorContent error={error} onReset={() => window.location.reload()} variant="component" />
      );
    },
  };
};

export default ErrorBoundary;
