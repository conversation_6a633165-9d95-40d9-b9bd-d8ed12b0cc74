import { apiClient } from '../httpClient';
import { ComponentDetails, TreeNodeType } from '@/utils/types';
import { createDirectoryRootNode, transformApiTreeToTreeNodes } from '@/utils/transformers';
import { ApiTreeResponse, RootIdResponse } from '@/types/api.types';
import { handleApiError, isUnauthorizedError } from '../error-handling';

/**
 * Gets the root component tree node ID
 */
export const getRootId = async (): Promise<string> => {
  try {
    const url = 'Components/tree/root';
    const response = await apiClient.get<RootIdResponse>(url);

    if (!response?.data?.id) {
      // Check if the response has objectId instead of id
      if (response?.data?.objectId) {
        return response.data.objectId;
      }
      throw new Error('Invalid response format - missing id');
    }

    return response.data.id;
  } catch (error: unknown) {
    // Centralize error shape and ensure status/code included upstream
    if (isUnauthorizedError(error)) {
      return 'unauthorized';
    }
    return handleApiError<string>(error, 'getting root ID');
  }
};

/**
 * Search for components in the tree by search term
 */
export const searchComponentsTree = async (findText: string): Promise<TreeNodeType[]> => {
  try {
    if (!findText || findText.trim() === '') {
      console.warn('[API] Empty search term provided');
      return [];
    }

    const path = `Components/tree/find`;
    const response = await apiClient.get<ApiTreeResponse>(path, {
      params: {
        findText,
        pageNumber: 1,
        pageSize: 100,
      },
    });

    if (!response.data) {
      throw new Error('Empty response received');
    }

    return transformApiTreeToTreeNodes(response.data);
  } catch (error: unknown) {
    return handleApiError(error, 'searching components', findText);
  }
};

/**
 * Gets children for a component tree node
 */
export const fetchTreeNodes = async (
  id: string,
  pageNumber = 1,
  pageSize = 50,
  options?: { signal?: AbortSignal }
): Promise<{
  nodes: TreeNodeType[];
  hasMorePages: boolean;
  totalCount: number;
}> => {
  try {
    if (!id) {
      console.warn('[API] No node ID provided for fetching tree nodes');
      return { nodes: [], hasMorePages: false, totalCount: 0 };
    }

    const path = `Components/tree/${id}/children`;

    const response = await apiClient.get<ApiTreeResponse>(path, {
      params: { pageNumber, pageSize },
      signal: options?.signal, // Support abort signal
    });

    if (!response.data) {
      throw new Error('Empty response received');
    }

    const transformedNodes = transformApiTreeToTreeNodes(response.data);
    const totalCount = response.data.pageInfo?.totalRecords || transformedNodes.length;
    const hasMorePages = totalCount > pageNumber * pageSize;

    return {
      nodes: transformedNodes,
      hasMorePages,
      totalCount,
    };
  } catch (error: unknown) {
    // Standardize to throw via centralized handler (include explicit type for TS)
    return handleApiError<{
      nodes: TreeNodeType[];
      hasMorePages: boolean;
      totalCount: number;
    }>(error, 'getting component children', id);
  }
};

/**
 * Gets component details for a specific component
 */
export const fetchComponentDetails = async (componentId: string): Promise<ComponentDetails> => {
  try {
    if (!componentId) {
      throw new Error('No component ID provided');
    }

    const path = `Components/${componentId}`;
    const response = await apiClient.get<ComponentDetails>(path);
    return response.data;
  } catch (error: unknown) {
    return handleApiError(error, 'getting component details', componentId);
  }
};

/**
 * Gets the initial component tree structure
 */
export const createTree = async (
  directoryId?: string,
  includeDirectory = false
): Promise<TreeNodeType[]> => {
  try {
    const nodeId = directoryId || (await getRootId());

    if (nodeId === 'unauthorized') {
      throw new Error('unauthorized');
    }

    const result = await fetchTreeNodes(nodeId);
    const children = result.nodes;

    if (!includeDirectory || !directoryId) {
      return children;
    }

    try {
      const directoryResponse = await apiClient.get<{
        id: string;
        name: string;
        nodeType: string;
      }>(`Components/${directoryId}`);

      if (!directoryResponse.data) {
        return children;
      }

      return createDirectoryRootNode(directoryResponse.data, children);
    } catch (error) {
      console.warn('Failed to get directory details, falling back to children only', error);
      return children;
    }
  } catch (error) {
    return handleApiError(error, 'getting initial component tree');
  }
};

/**
 * Fetches more tree nodes and combines them with existing nodes
 * @param id - Node ID
 * @param existingNodes - Existing tree nodes to combine with new ones
 * @param pageNumber - Current page number
 * @param pageSize - Page size for pagination
 */
export const fetchMoreTreeNodes = async (
  id: string,
  existingNodes: TreeNodeType[],
  pageNumber = 1,
  pageSize = 50
): Promise<{
  nodes: TreeNodeType[];
  hasMorePages: boolean;
  totalCount: number;
  nextPage: number;
}> => {
  try {
    if (!id) {
      console.warn('[API] No node ID provided for fetching more tree nodes');
      return {
        nodes: existingNodes,
        hasMorePages: false,
        totalCount: existingNodes.length,
        nextPage: pageNumber + 1,
      };
    }

    // Fetch the next page of nodes
    const result = await fetchTreeNodes(id, pageNumber + 1, pageSize);
    const combinedNodes = [...existingNodes, ...result.nodes];

    return {
      nodes: combinedNodes,
      hasMorePages: result.hasMorePages,
      totalCount: result.totalCount,
      nextPage: pageNumber + 2,
    };
  } catch (error: unknown) {
    console.error(`[API] Error fetching more tree nodes for ${id}:`, error);
    return {
      nodes: existingNodes,
      hasMorePages: false,
      totalCount: existingNodes.length,
      nextPage: pageNumber + 1,
    };
  }
};
