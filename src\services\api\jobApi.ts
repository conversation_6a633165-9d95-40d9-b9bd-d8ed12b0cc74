import { apiClient } from '../httpClient';
import { Job, JobResult } from '@/utils/types';
import { transformJob, transformJobResult } from '@/utils/transformers';
import { JobApiResponse, JobResultApiResponse, JobStatusResponse } from '@/types/api.types';
import { handleApiError } from '../error-handling';
import { JobStatus, transformApiStatus } from '@/utils/jobs';
import axios from 'axios';

interface FetchJobsOptions {
  includeStatus?: boolean;
  includeLatestResult?: boolean;
}

/**
 * Gets the status of a specific job
 */
export const fetchJobStatus = async (jobId: string): Promise<JobStatus> => {
  try {
    if (!jobId) {
      console.warn('[API] No job ID provided for fetching job status');
      return JobStatus.Waiting;
    }

    const statusPath = `Jobs/${jobId}/status`;
    const response = await apiClient.get<JobStatusResponse>(statusPath);

    if (!response.data?.status) {
      console.warn('[API] No status returned for job:', jobId);
      return JobStatus.Waiting;
    }

    return transformApiStatus(response.data.status);
  } catch (error: unknown) {
    console.warn(`[API] Failed to fetch status for job ${jobId}:`, error);
    return JobStatus.Waiting;
  }
};

/**
 * Gets jobs for a specific component
 */
export const fetchJobs = async (
  componentId: string,
  options: FetchJobsOptions = {}
): Promise<{ jobs: Job[] }> => {
  try {
    if (!componentId) {
      console.warn('[API] No component ID provided for fetching jobs');
      return { jobs: [] };
    }

    const { includeStatus = true, includeLatestResult = true } = options;

    const jobsPath = 'Jobs';
    const jobsResponse = await apiClient.get<JobApiResponse>(jobsPath, {
      params: { componentId },
    });

    if (!jobsResponse.data?.jobs || jobsResponse.data.jobs.length === 0) {
      console.warn('[API] No jobs returned for component:', componentId);
      return { jobs: [] };
    }

    const enhancedJobs = await Promise.all(
      jobsResponse.data.jobs.map(async job => {
        const requests: Promise<any>[] = [];

        if (includeStatus) {
          requests.push(
            apiClient.get<JobStatusResponse>(`Jobs/${job.id}/status`).catch(error => {
              console.warn(`[API] Failed to fetch status for job ${job.id}:`, error);
              return { data: null };
            })
          );
        }

        if (includeLatestResult) {
          requests.push(
            fetchJobResults(job.id, { pageNumber: 1, pageSize: 1 }).catch(error => {
              console.warn(`[API] Failed to fetch latest result for job ${job.id}:`, error);
              return { results: [], totalRecords: 0 };
            })
          );
        }

        const [status, results] = await Promise.all(requests);

        return {
          ...transformJob(job),
          ...(status?.data && { status: status.data }),
          ...(results?.results && { results: results.results }),
        };
      })
    );

    return { jobs: enhancedJobs };
  } catch (error: unknown) {
    return handleApiError(error, 'getting component jobs', componentId);
  }
};

interface FetchJobResultsOptions {
  pageNumber?: number;
  pageSize?: number;
  sortBy?: 'timestamp' | 'status';
  sortDirection?: 'asc' | 'desc';
}

/**
 * Gets job results for a specific job with pagination and sorting support
 */
export const fetchJobResults = async (
  jobId: string,
  options: FetchJobResultsOptions = {}
): Promise<{ results: JobResult[]; totalRecords: number }> => {
  try {
    if (!jobId) {
      console.warn('[API] No job ID provided for fetching jobs');
      return { results: [], totalRecords: 0 };
    }

    const { pageNumber = 1, pageSize = 50, sortBy = 'timestamp', sortDirection = 'desc' } = options;

    const jobDetailsPath = `Jobs/${jobId}/results`;
    const resultResponse = await apiClient.get<JobResultApiResponse>(jobDetailsPath, {
      params: {
        pageNumber,
        pageSize,
        sortBy,
        sortDirection,
      },
    });

    let transformedJobResults: JobResult[] = [];

    if (resultResponse.data?.results?.length) {
      transformedJobResults = resultResponse.data.results
        .map(transformJobResult)
        .filter((result): result is JobResult => result !== undefined);
    }

    return {
      results: transformedJobResults,
      totalRecords: resultResponse.data?.pageInfo?.totalRecords || 0,
    };
  } catch (error) {
    // Delegate 404-empties logic to centralized handler to avoid duplication
    return handleApiError<{ results: JobResult[]; totalRecords: number }>(
      error,
      'getting jobs results',
      jobId
    );
  }
};
