import { http, HttpResponse, delay } from 'msw';

export const jobsHandlers = [
  // 500 Internal Server Error when creating a job
  http.post('*/api/jobs', () =>
    HttpResponse.json({ error: 'Internal error' }, { status: 500 })
  ),

  // 404 Not Found for a specific job
  http.get('*/api/jobs/:id', () =>
    HttpResponse.json({ detail: 'Not found' }, { status: 404 })
  ),

  // 429 Too Many Requests with Retry-After
  http.get('*/api/jobs/search', () =>
    new HttpResponse(JSON.stringify({ message: 'Rate limited' }), {
      status: 429,
      headers: { 'Retry-After': '30' },
    })
  ),

  // Simulated timeout: let axios hit its timeout
  http.get('*/api/jobs/slow', async () => {
    await delay('infinite');
    return HttpResponse.json({});
  }),
];

