import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { useTranslation } from 'react-i18next';
import { getErrorMessage } from '@/utils/errors';

interface AuthErrorProps {
  onRetry?: () => void;
  customError?: Error | string;
}

/**
 * Authentication error display component
 * Shows detailed error information for authentication issues
 * and provides a retry button
 */
export const AuthError: React.FC<AuthErrorProps> = ({ onRetry, customError }) => {
  const { error } = useAuth();
  const { t } = useTranslation();
  const displayError = customError || error;

  // Parse error message to be more user-friendly and include error codes
  const getFormattedErrorMessage = () => {
    if (!displayError) return t('auth.unknownError', 'An unknown error occurred');

    // Use the enhanced getErrorMessage utility that includes error codes
    const errorMessage = getErrorMessage(displayError, true);

    // Check for common error patterns and provide user-friendly messages while preserving error codes
    if (errorMessage.includes('URL')) {
      const codeMatch = errorMessage.match(/\(code: ([^)]+)\)/);
      const codeText = codeMatch ? ` ${codeMatch[0]}` : '';
      return t('auth.urlError', 'There was a problem with the authentication server URL') + codeText;
    } else if (errorMessage.includes('timeout')) {
      const codeMatch = errorMessage.match(/\(code: ([^)]+)\)/);
      const codeText = codeMatch ? ` ${codeMatch[0]}` : '';
      return t('auth.timeoutError', 'The authentication request timed out') + codeText;
    } else if (errorMessage.includes('network')) {
      const codeMatch = errorMessage.match(/\(code: ([^)]+)\)/);
      const codeText = codeMatch ? ` ${codeMatch[0]}` : '';
      return t('auth.networkError', 'A network error occurred. Please check your connection') + codeText;
    } else if (errorMessage.includes('state')) {
      const codeMatch = errorMessage.match(/\(code: ([^)]+)\)/);
      const codeText = codeMatch ? ` ${codeMatch[0]}` : '';
      return t('auth.stateError', 'The authentication state was lost or invalid') + codeText;
    }

    return errorMessage;
  };

  // Get technical details for developers
  const getTechnicalDetails = () => {
    if (!displayError) return '';
    return typeof displayError === 'string' ? displayError : displayError.message;
  };

  // When the user clicks the retry button
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      // If no retry function is provided, refresh the page
      window.location.reload();
    }
  };

  return (
    <div className="flex-center-col p-4 bg-background rounded-lg shadow-md">
      <h2 className="text-xl font-semibold text-error mb-4">
        {t('auth.errorTitle', 'Authentication Error')}
      </h2>

      <div className="mb-4 text-center">
        <p className="text-primary mb-2">{getFormattedErrorMessage()}</p>
        <p className="text-sm text-border-color-dark mt-2">
          {t('auth.tryAgainMessage', 'Please try again or contact support')}
        </p>
      </div>

      {/* Technical details for developers - collapsed by default */}
      <details className="mb-4 w-full">
        <summary className="text-sm text-primary-light cursor-pointer">
          {t('common.technicalDetails', 'Technical Details')}
        </summary>
        <pre className="mt-2 p-2 bg-secondary-background rounded text-xs overflow-auto max-w-full">
          {getTechnicalDetails()}
        </pre>
      </details>

      <button
        onClick={handleRetry}
        className="px-4 py-2 bg-primary text-inverted-light rounded-md hover:bg-primary-dark transition-colors"
      >
        {t('auth.tryAgain', 'Try Again')}
      </button>
    </div>
  );
};
