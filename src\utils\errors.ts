// src/utils/errors.ts
import { AxiosError } from 'axios';
import { ApiErrorResponse } from '@/types/api.types';

// Re-export the main ApiError from error-handling for consistency
export { ApiError } from '@/services/error-handling';

// Legacy ApiError class - kept for backward compatibility but deprecated
// @deprecated Use ApiError from @/services/error-handling instead
export class LegacyApiError extends Error {
  constructor(
    message: string,
    public readonly context: string,
    public readonly originalError: unknown
  ) {
    super(message);
    this.name = 'LegacyApiError';
  }
}

export class TransformationError extends Error {
  constructor(
    message: string,
    public readonly context: string,
    public readonly originalData: unknown
  ) {
    super(message);
    this.name = 'TransformationError';
  }
}

/**
 * Enhanced error message extraction that includes error codes
 * @param error - The error to extract message from
 * @param includeCode - Whether to include error code in the message (default: true)
 * @returns Formatted error message with optional error code
 */
export const getErrorMessage = (error: unknown, includeCode: boolean = true): string => {
  if (error && typeof error === 'object' && 'isAxiosError' in error) {
    const axiosError = error as AxiosError<ApiErrorResponse>;
    const responseData = axiosError.response?.data;
    const responseMessage = responseData?.message || responseData?.error;

    // Extract error code from response
    const errorCode = responseData?.errorCode ||
                     responseData?.error_code ||
                     responseData?.code ||
                     responseData?.type ||
                     (axiosError as any).code;

    let message = responseMessage || axiosError.message || 'Unknown API error';

    // Append error code if available and requested
    if (includeCode && errorCode && typeof errorCode === 'string') {
      message += ` (code: ${errorCode})`;
    }

    return message;
  }

  // Handle our enhanced ApiError
  if (error && typeof error === 'object' && 'name' in error && (error as any).name === 'ApiError') {
    const apiError = error as any;
    let message = apiError.message || 'API error occurred';

    // Add error code if available and not already in message
    if (includeCode && apiError.code && typeof apiError.code === 'string' && !message.includes(`code: ${apiError.code}`)) {
      message += ` (code: ${apiError.code})`;
    }

    return message;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'Unknown error occurred';
};

/**
 * Formats error messages for toast notifications with error codes
 * @param baseMessage - The base user-friendly message
 * @param error - The error object to extract code from
 * @returns Formatted message with error code appended if available
 */
export const formatToastErrorMessage = (baseMessage: string, error: unknown): string => {
  const errorMessage = getErrorMessage(error, true);
  const codeMatch = errorMessage.match(/\(code: ([^)]+)\)/);
  const codeText = codeMatch ? ` ${codeMatch[0]}` : '';
  return `${baseMessage}${codeText}`;
};

/**
 * Extracts just the error code from an error object
 * @param error - The error object to extract code from
 * @returns The error code string or undefined if not found
 */
export const extractErrorCode = (error: unknown): string | undefined => {
  // Handle our enhanced ApiError
  if (error && typeof error === 'object' && 'name' in error && (error as any).name === 'ApiError') {
    const apiError = error as any;
    if (apiError.code && typeof apiError.code === 'string') {
      return apiError.code;
    }
  }

  // Handle axios errors
  if (error && typeof error === 'object' && 'isAxiosError' in error) {
    const axiosError = error as AxiosError<ApiErrorResponse>;
    const responseData = axiosError.response?.data;

    // Try multiple error code field names
    const errorCode = responseData?.errorCode ||
                     responseData?.error_code ||
                     responseData?.code ||
                     responseData?.type ||
                     (axiosError as any).code;

    if (errorCode && typeof errorCode === 'string') {
      return errorCode;
    }
  }

  // Handle generic errors with code property
  if (error && typeof error === 'object' && 'code' in error) {
    const errorCode = (error as any).code;
    if (errorCode && typeof errorCode === 'string') {
      return errorCode;
    }
  }

  return undefined;
};
