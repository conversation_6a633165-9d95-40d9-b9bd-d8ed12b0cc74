import { http, HttpResponse } from 'msw';

// OAuth and protected endpoints error scenarios
export const authHandlers = [
  // Simulate token refresh success
  http.post('*/v1/oauth2/token', async () => {
    return HttpResponse.json({
      access_token: 'new_access_token',
      token_type: 'Bearer',
      expires_in: 3600,
    });
  }),

  // Force 401 for protected resource example
  http.get('*/api/secure', () =>
    HttpResponse.json({ message: 'Unauthorized' }, { status: 401 })
  ),
];

