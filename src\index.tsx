import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.scss';
import App from './App';
import { getConfig, loadConfig } from './config';
import './i18n'; // Import the i18n configuration

/**
 * Renders the React application into the DOM.
 * This function is called after the runtime configuration is loaded.
 */
const renderApp = () => {
  const container = document.getElementById('root');
  if (container) {
    const root = ReactDOM.createRoot(container);
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
  } else {
    console.error('Root container not found. Application cannot be mounted.');
  }
};

/**use
 * Initializes the application.
 * It first loads the runtime configuration and then renders the app.
 * This approach prevents race conditions where the app might need
 * configuration values before they are available.
 */
const initializeApp = async () => {
  try {
    // Wait for the configuration to be loaded from config.js
    // The `loadConfig` function is designed to read `window.APP_CONFIG`
    await loadConfig({ throwOnError: true, enableLogging: true });

    // Conditionally enable MSW based on runtime config
    try {
      const config = getConfig();
      const shouldMock = (config as any).MOCK_API === true;
      if (shouldMock && typeof window !== 'undefined') {
        const { worker } = await import('./mocks/browser');
        await worker.start({
          onUnhandledRequest: 'bypass',
          serviceWorker: { url: '/mockServiceWorker.js' },
        });
        console.log('[MSW] API mocking enabled');
      }
    } catch (e) {
      console.warn('[MSW] Skipping API mocking initialization:', e);
    }
    console.log('[Bootstrap] Configuration validated, starting application...');
    renderApp();
  } catch (error) {
    console.error('[Bootstrap] Failed to initialize application:', error);
    const container = document.getElementById('root');
    if (container) {
      container.innerHTML = `
        <div style="padding: 20px; text-align: center; font-family: sans-serif; color: #333;">
          <h1>Application Error</h1>
          <p>Could not load necessary configuration to start the application.</p>
          <p>Please check the console for more details.</p>
        </div>
      `;
    }
  }
};

// Start the application initialization process
initializeApp();
